services:
  # Nacos服务注册中心
  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: order-check-nacos
    environment:
      - MODE=standalone
      - SPRING_DATASOURCE_PLATFORM=mysql
      - MYSQL_SERVICE_HOST=host.docker.internal
      - MYSQL_SERVICE_PORT=3306
      - MYSQL_SERVICE_DB_NAME=nacos
      - MYSQL_SERVICE_USER=root
      - MYSQL_SERVICE_PASSWORD=Hwc132465
      - MYSQL_SERVICE_DB_PARAM=characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true
    ports:
      - "8848:8848"
      - "9848:9848"
    restart: always
    networks:
      - order-check-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # 订单查询服务
  order-query-service:
    build:
      context: .
      dockerfile: order-query-service/Dockerfile
    container_name: order-query-service
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - NACOS_SERVER_ADDR=nacos:8848
      - MYSQL_HOST=*************
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=dytkf
      - MYSQL_USERNAME=dyt
      - MYSQL_PASSWORD=dyt
      - REDIS_HOST=order-check-redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=HdkcRedis123!@#
      - JAVA_OPTS=-Dspringfox.documentation.enabled=false
      - REDIS_HOST=host.docker.internal
      - REDIS_PORT=6379
      - REDIS_PASSWORD=HdkcRedis123!@#
    depends_on:
      - nacos
      - order-check-redis
    restart: always
    networks:
      - order-check-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # 订单检查服务
  order-check-service:
    build:
      context: .
      dockerfile: order-check-service/Dockerfile
    container_name: order-check-service
    ports:
      - "8082:8082"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - NACOS_SERVER_ADDR=nacos:8848
      - REDIS_HOST=order-check-redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=HdkcRedis123!@#
      - JAVA_OPTS=-Dspringfox.documentation.enabled=false
    depends_on:
      - nacos
      - order-check-redis
    restart: always
    networks:
      - order-check-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # 结果存储服务
  order-result-service:
    build:
      context: .
      dockerfile: order-result-service/Dockerfile
    container_name: order-result-service
    ports:
      - "8083:8083"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - NACOS_SERVER_ADDR=nacos:8848
      - MYSQL_HOST=host.docker.internal
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=order_check_result
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=Hwc132465
      - REDIS_HOST=order-check-redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=HdkcRedis123!@#
      - JAVA_OPTS=-Dspringfox.documentation.enabled=false
    depends_on:
      - nacos
      - order-check-redis
    restart: always
    networks:
      - order-check-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # 前端服务
  order-web-frontend:
    build:
      context: .
      dockerfile: order-web-frontend/Dockerfile
    container_name: order-web-frontend
    ports:
      - "8080:80"
    depends_on:
      - order-query-service
      - order-check-service
      - order-result-service
    restart: always
    networks:
      - order-check-network

  # Redis服务
  order-check-redis:
    image: redis:7.0-alpine
    container_name: order-check-redis
    ports:
      - "6380:6379"
    command: redis-server --requirepass HdkcRedis123!@#
    volumes:
      - redis-data:/data
    networks:
      - order-check-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "HdkcRedis123!@#", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis-data:
    driver: local

networks:
  order-check-network:
    driver: bridge
